#===============================================================================
# Custom Particle Effects for Seasonal Form Changes
# Creates beautiful seasonal-themed particle effects when Pokemon change forms
#===============================================================================

module FollowingPkmn
  #=============================================================================
  # Enhanced seasonal change animation with custom particles
  #=============================================================================
  def self.play_enhanced_seasonal_animation(season = nil)
    return if !FollowingPkmn.active? || !FollowingPkmn::SHOW_SEASONAL_CHANGE_ANIMATION

    event = FollowingPkmn.get_event
    return if !event

    # Determine the season if not provided
    season = pbGetSeason if !season

    # Play the base sparkle animation
    $scene.spriteset.addUserAnimation(FollowingPkmn::ANIMATION_SEASONAL_CHANGE,
                                      event.x, event.y, true, 3)

    # Add seasonal-specific effects
    case season
    when 0  # Spring
      create_spring_particles(event)
      pbSEPlay("Pkmn move learnt") if defined?(pbSEPlay)
    when 1  # Summer
      create_summer_particles(event)
      pbSEPlay("Pkmn move learnt") if defined?(pbSEPlay)
    when 2  # Autumn
      create_autumn_particles(event)
      pbSEPlay("Pkmn move learnt") if defined?(pbSEPlay)
    when 3  # Winter
      create_winter_particles(event)
      pbSEPlay("Pkmn move learnt") if defined?(pbSEPlay)
    end
  end

  #=============================================================================
  # Season-specific particle effects
  #=============================================================================

  # Spring: Green sparkles and flower petals
  def self.create_spring_particles(event)
    return if !$scene.spriteset.respond_to?(:addUserAnimation)

    # Create multiple small sparkles around the Pokemon
    3.times do |i|
      offset_x = rand(-16..16)
      offset_y = rand(-16..16)

      # Use a slight delay for each particle
      pbWait(i * 3) if defined?(pbWait)

      $scene.spriteset.addUserAnimation(FollowingPkmn::ANIMATION_SEASONAL_CHANGE,
                                        event.x + offset_x, event.y + offset_y, true, 2)
    end
  end

  # Summer: Bright yellow/orange sparkles
  def self.create_summer_particles(event)
    return if !$scene.spriteset.respond_to?(:addUserAnimation)

    # Create a burst of sparkles
    4.times do |i|
      angle = (i * 90) * Math::PI / 180  # 90 degree intervals
      radius = 20
      offset_x = (radius * Math.cos(angle)).to_i
      offset_y = (radius * Math.sin(angle)).to_i

      pbWait(i * 2) if defined?(pbWait)

      $scene.spriteset.addUserAnimation(FollowingPkmn::ANIMATION_SEASONAL_CHANGE,
                                        event.x + offset_x, event.y + offset_y, true, 2)
    end
  end

  # Autumn: Orange/red falling leaves effect
  def self.create_autumn_particles(event)
    return if !$scene.spriteset.respond_to?(:addUserAnimation)

    # Create falling leaf-like particles
    5.times do |i|
      offset_x = rand(-24..24)
      offset_y = rand(-32..-8)  # Start above the Pokemon

      pbWait(i * 4) if defined?(pbWait)

      $scene.spriteset.addUserAnimation(FollowingPkmn::ANIMATION_SEASONAL_CHANGE,
                                        event.x + offset_x, event.y + offset_y, true, 1)
    end
  end

  # Winter: Blue/white snowflake effect
  def self.create_winter_particles(event)
    return if !$scene.spriteset.respond_to?(:addUserAnimation)

    # Create a swirling snowflake pattern
    6.times do |i|
      angle = (i * 60) * Math::PI / 180  # 60 degree intervals for hexagonal pattern
      radius = 16
      offset_x = (radius * Math.cos(angle)).to_i
      offset_y = (radius * Math.sin(angle)).to_i

      pbWait(i * 2) if defined?(pbWait)

      $scene.spriteset.addUserAnimation(FollowingPkmn::ANIMATION_SEASONAL_CHANGE,
                                        event.x + offset_x, event.y + offset_y, true, 3)
    end
  end

  #=============================================================================
  # Alternative simple flash effect for systems without animation support
  #=============================================================================
  def self.play_simple_seasonal_flash
    return if !FollowingPkmn.active?

    # Create a simple screen flash effect
    if $scene && $scene.spriteset && $scene.spriteset.viewport1
      viewport = $scene.spriteset.viewport1

      # Determine flash color based on season
      case pbGetSeason
      when 0  # Spring - Green flash
        flash_color = Color.new(0, 255, 0, 128)
      when 1  # Summer - Yellow flash
        flash_color = Color.new(255, 255, 0, 128)
      when 2  # Autumn - Orange flash
        flash_color = Color.new(255, 128, 0, 128)
      when 3  # Winter - Blue flash
        flash_color = Color.new(128, 128, 255, 128)
      else
        flash_color = Color.new(255, 255, 255, 128)
      end

      # Flash the screen briefly
      viewport.flash(flash_color, 20)

      # Play sound effect
      pbSEPlay("Pkmn move learnt") if defined?(pbSEPlay)
    end
  end

  #=============================================================================
  # Main method that chooses the best available effect
  #=============================================================================
  def self.play_seasonal_change_animation_enhanced
    return if !FollowingPkmn.active? || !FollowingPkmn::SHOW_SEASONAL_CHANGE_ANIMATION

    # Check if enhanced particles are enabled
    if FollowingPkmn::USE_ENHANCED_SEASONAL_PARTICLES
      # Try enhanced animation first, fall back to simple methods
      if $scene && $scene.spriteset && $scene.spriteset.respond_to?(:addUserAnimation)
        play_enhanced_seasonal_animation
      elsif defined?(pbFlash)
        play_simple_seasonal_flash
      else
        # Fallback: just play a sound
        pbSEPlay("Pkmn move learnt") if defined?(pbSEPlay)
      end
    else
      # Use simple sparkle animation only
      event = FollowingPkmn.get_event
      if event && $scene.spriteset.respond_to?(:addUserAnimation)
        $scene.spriteset.addUserAnimation(FollowingPkmn::ANIMATION_SEASONAL_CHANGE,
                                          event.x, event.y, true, 3)
      end
      pbSEPlay("Pkmn move learnt") if defined?(pbSEPlay)
    end
  end
end
